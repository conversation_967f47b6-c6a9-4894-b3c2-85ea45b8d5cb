// 清理IP地址中的端口号
function cleanIPAddress(ip) {
    if (!ip) return ip;

    // 对于IPv6地址，移除末尾的端口号
    if (ip.includes(':')) {
        // 检查是否有端口号（最后一个冒号后面是数字）
        const lastColonIndex = ip.lastIndexOf(':');
        const afterLastColon = ip.substring(lastColonIndex + 1);

        // 如果最后一个冒号后面是纯数字且不是IPv6地址的一部分，则移除它
        if (/^\d+$/.test(afterLastColon) && lastColonIndex > ip.indexOf('::')) {
            return ip.substring(0, lastColonIndex);
        }
    }

    return ip;
}

// 验证IP地址是否合法
function isValidIP(ip) {
    if (!ip) return false;

    // 先清理IP地址
    const cleanedIP = cleanIPAddress(ip);

    const ipv4Pattern =
        /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

    // 更完整的IPv6正则表达式
    const ipv6Pattern =
        /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/;

    return ipv4Pattern.test(cleanedIP) || ipv6Pattern.test(cleanedIP);
};

export { isValidIP, cleanIPAddress };