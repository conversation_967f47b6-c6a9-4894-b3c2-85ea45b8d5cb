import { isValidIP } from '@/utils/valid-ip.js';

// 从 Cloudflare 中国获取 IP 地址
const getIPFromCloudflare_CN = async () => {
    const source = "CF-CN";

    // 首先尝试通过本地 API 获取 IP
    try {
        const response = await fetch('/api/ipapicom');
        if (response.ok) {
            const data = await response.json();
            if (data.ip && isValidIP(data.ip)) {
                return {
                    ip: data.ip,
                    source: source
                };
            }
        }
    } catch (error) {
        // 继续尝试其他方法
    }

    // 备用方案：尝试多个 Cloudflare 中国端点
    const endpoints = [
        "https://cf-ns.com/cdn-cgi/trace",
        "https://cloudflare.com/cdn-cgi/trace",
        "https://www.cloudflare.com/cdn-cgi/trace"
    ];

    for (const endpoint of endpoints) {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            const response = await fetch(endpoint, {
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (response.ok) {
                const data = await response.text();
                const lines = data.split("\n");
                const ipLine = lines.find((line) => line.startsWith("ip="));
                if (ipLine) {
                    const ip = ipLine.split("=")[1].trim();
                    if (isValidIP(ip)) {
                        return { ip: ip, source: source };
                    }
                }
            }
        } catch (error) {
            // 继续尝试下一个端点
            continue;
        }
    }

    // 所有方法都失败
    return {
        ip: null,
        source: source
    };
};

export { getIPFromCloudflare_CN };